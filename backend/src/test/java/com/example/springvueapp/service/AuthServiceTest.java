package com.example.springvueapp.service;

import com.example.springvueapp.entity.UserEntity;
import com.example.springvueapp.model.User;
import com.example.springvueapp.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AuthService 的单元测试
 * 使用 Mockito 模拟依赖
 */
@ExtendWith(MockitoExtension.class)
public class AuthServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private JwtService jwtService;

    @InjectMocks
    private AuthService authService;

    private UserEntity testUser;

    @BeforeEach
    public void setUp() {
        // 创建测试用户
        testUser = UserEntity.builder()
                .id(1L)
                .username("testuser")
                .password("encodedPassword")
                .email("<EMAIL>")
                .fullName("Test User")
                .enabled(true)
                .build();
    }

    @Test
    public void testAuthenticateUser_Success() {
        // 模拟依赖行为
        when(userRepository.findByUsername("testuser")).thenReturn(Mono.just(testUser));
        when(passwordEncoder.matches("password", "encodedPassword")).thenReturn(true);
        when(jwtService.generateTokenMono("testuser")).thenReturn(Mono.just("jwt.token.here"));

        // 调用被测试方法
        Mono<String> tokenMono = authService.authenticateUser("testuser", "password");

        // 验证结果
        StepVerifier.create(tokenMono)
                .expectNext("jwt.token.here")
                .verifyComplete();
    }

    @Test
    public void testAuthenticateUser_WrongPassword() {
        // 模拟依赖行为
        when(userRepository.findByUsername("testuser")).thenReturn(Mono.just(testUser));
        when(passwordEncoder.matches("wrongpassword", "encodedPassword")).thenReturn(false);

        // 调用被测试方法
        Mono<String> tokenMono = authService.authenticateUser("testuser", "wrongpassword");

        // 验证结果
        StepVerifier.create(tokenMono)
                .expectError(IllegalArgumentException.class)
                .verify();
    }

    @Test
    public void testAuthenticateUser_UserNotFound() {
        // 模拟依赖行为
        when(userRepository.findByUsername("nonexistentuser")).thenReturn(Mono.empty());

        // 调用被测试方法
        Mono<String> tokenMono = authService.authenticateUser("nonexistentuser", "password");

        // 验证结果
        StepVerifier.create(tokenMono)
                .expectError(IllegalArgumentException.class)
                .verify();
    }

    @Test
    public void testRegisterUser_Success() {
        // 创建注册用户
        User newUser = User.builder()
                .username("newuser")
                .password("password")
                .email("<EMAIL>")
                .fullName("New User")
                .build();

        // 模拟依赖行为
        when(userRepository.existsByUsername("newuser")).thenReturn(Mono.just(false));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(Mono.just(false));
        when(passwordEncoder.encode("password")).thenReturn("encodedPassword");
        when(userRepository.save(any(UserEntity.class))).thenAnswer(invocation -> {
            User savedUser = invocation.getArgument(0);
            savedUser.setId(2L);
            return Mono.just(savedUser);
        });

        // 调用被测试方法
        Mono<Boolean> resultMono = authService.registerUser(newUser);

        // 验证结果
        StepVerifier.create(resultMono)
                .expectNext(true)
                .verifyComplete();
    }

    @Test
    public void testRegisterUser_UsernameExists() {
        // 创建注册用户
        User newUser = User.builder()
                .username("existinguser")
                .password("password")
                .email("<EMAIL>")
                .fullName("New User")
                .build();

        // 模拟依赖行为
        when(userRepository.existsByUsername("existinguser")).thenReturn(Mono.just(true));
        when(userRepository.existsByEmail("<EMAIL>")).thenReturn(Mono.just(false));

        // 调用被测试方法
        Mono<Boolean> resultMono = authService.registerUser(newUser);

        // 验证结果
        StepVerifier.create(resultMono)
                .expectNext(false)
                .verifyComplete();
    }
}
