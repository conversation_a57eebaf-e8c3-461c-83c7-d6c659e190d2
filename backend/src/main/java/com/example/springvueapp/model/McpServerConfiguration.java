package com.example.springvueapp.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * MCP 服务器配置的DTO类
 * 与前端TypeScript接口保持一致，用于API交互
 */
public class McpServerConfiguration {

    private Long id;

    @NotBlank(message = "Name is required")
    private String name;

    private String description;

    @NotBlank(message = "Command is required")
    private String command;

    private List<String> arguments;

    private Map<String, String> environment;

    private String workingDirectory;

    @NotBlank(message = "Docker image is required")
    private String dockerImage;

    private ResourceLimits resourceLimits;

    private NetworkConfig networkConfig;

    private List<VolumeMount> volumeMounts;

    private Integer timeoutSeconds;

    private Boolean autoRestart = false;

    private Boolean enabled = true;

    @NotNull
    private Long userId; // 此配置的所有者

    private LocalDateTime createdAt;

    private LocalDateTime updatedAt;

    // 私有构造函数，只能通过构建器创建实例
    private McpServerConfiguration() {}

    // 私有构造函数，通过构建器传入参数
    private McpServerConfiguration(Builder builder) {
        this.id = builder.id;
        this.name = builder.name;
        this.description = builder.description;
        this.command = builder.command;
        this.arguments = builder.arguments;
        this.environment = builder.environment;
        this.workingDirectory = builder.workingDirectory;
        this.dockerImage = builder.dockerImage;
        this.resourceLimits = builder.resourceLimits;
        this.networkConfig = builder.networkConfig;
        this.volumeMounts = builder.volumeMounts;
        this.timeoutSeconds = builder.timeoutSeconds;
        this.autoRestart = builder.autoRestart;
        this.enabled = builder.enabled;
        this.userId = builder.userId;
        this.createdAt = builder.createdAt;
        this.updatedAt = builder.updatedAt;
    }

    // 静态构建器类
    public static class Builder {
        private Long id;
        private String name;
        private String description;
        private String command;
        private List<String> arguments;
        private Map<String, String> environment;
        private String workingDirectory;
        private String dockerImage;
        private ResourceLimits resourceLimits;
        private NetworkConfig networkConfig;
        private List<VolumeMount> volumeMounts;
        private Integer timeoutSeconds;
        private Boolean autoRestart = false;
        private Boolean enabled = true;
        private Long userId;
        private LocalDateTime createdAt;
        private LocalDateTime updatedAt;

        // 设置 id 的方法
        public Builder id(Long id) {
            this.id = id;
            return this;
        }

        // 设置 name 的方法
        public Builder name(String name) {
            this.name = name;
            return this;
        }

        // 设置 description 的方法
        public Builder description(String description) {
            this.description = description;
            return this;
        }

        // 设置 command 的方法
        public Builder command(String command) {
            this.command = command;
            return this;
        }

        // 设置 arguments 的方法
        public Builder arguments(List<String> arguments) {
            this.arguments = arguments;
            return this;
        }

        // 设置 environment 的方法
        public Builder environment(Map<String, String> environment) {
            this.environment = environment;
            return this;
        }

        // 设置 workingDirectory 的方法
        public Builder workingDirectory(String workingDirectory) {
            this.workingDirectory = workingDirectory;
            return this;
        }

        // 设置 dockerImage 的方法
        public Builder dockerImage(String dockerImage) {
            this.dockerImage = dockerImage;
            return this;
        }

        // 设置 resourceLimits 的方法
        public Builder resourceLimits(ResourceLimits resourceLimits) {
            this.resourceLimits = resourceLimits;
            return this;
        }

        // 设置 networkConfig 的方法
        public Builder networkConfig(NetworkConfig networkConfig) {
            this.networkConfig = networkConfig;
            return this;
        }

        // 设置 volumeMounts 的方法
        public Builder volumeMounts(List<VolumeMount> volumeMounts) {
            this.volumeMounts = volumeMounts;
            return this;
        }

        // 设置 timeoutSeconds 的方法
        public Builder timeoutSeconds(Integer timeoutSeconds) {
            this.timeoutSeconds = timeoutSeconds;
            return this;
        }

        // 设置 autoRestart 的方法
        public Builder autoRestart(Boolean autoRestart) {
            this.autoRestart = autoRestart;
            return this;
        }

        // 设置 enabled 的方法
        public Builder enabled(Boolean enabled) {
            this.enabled = enabled;
            return this;
        }

        // 设置 userId 的方法
        public Builder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        // 设置 createdAt 的方法
        public Builder createdAt(LocalDateTime createdAt) {
            this.createdAt = createdAt;
            return this;
        }

        // 设置 updatedAt 的方法
        public Builder updatedAt(LocalDateTime updatedAt) {
            this.updatedAt = updatedAt;
            return this;
        }

        // 构建 McpServerConfiguration 实例
        public McpServerConfiguration build() {
            return new McpServerConfiguration(this);
        }
    }

    // 在 McpServerConfiguration 类中实现 builder() 方法
    public static Builder builder() {
        return new Builder();
    }

    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getCommand() { return command; }
    public void setCommand(String command) { this.command = command; }

    public List<String> getArguments() { return arguments; }
    public void setArguments(List<String> arguments) { this.arguments = arguments; }

    public Map<String, String> getEnvironment() { return environment; }
    public void setEnvironment(Map<String, String> environment) { this.environment = environment; }

    public String getWorkingDirectory() { return workingDirectory; }
    public void setWorkingDirectory(String workingDirectory) { this.workingDirectory = workingDirectory; }

    public String getDockerImage() { return dockerImage; }
    public void setDockerImage(String dockerImage) { this.dockerImage = dockerImage; }

    public ResourceLimits getResourceLimits() { return resourceLimits; }
    public void setResourceLimits(ResourceLimits resourceLimits) { this.resourceLimits = resourceLimits; }

    public NetworkConfig getNetworkConfig() { return networkConfig; }
    public void setNetworkConfig(NetworkConfig networkConfig) { this.networkConfig = networkConfig; }

    public List<VolumeMount> getVolumeMounts() { return volumeMounts; }
    public void setVolumeMounts(List<VolumeMount> volumeMounts) { this.volumeMounts = volumeMounts; }

    public Integer getTimeoutSeconds() { return timeoutSeconds; }
    public void setTimeoutSeconds(Integer timeoutSeconds) { this.timeoutSeconds = timeoutSeconds; }

    public Boolean getAutoRestart() { return autoRestart; }
    public void setAutoRestart(Boolean autoRestart) { this.autoRestart = autoRestart; }

    public Boolean getEnabled() { return enabled; }
    public void setEnabled(Boolean enabled) { this.enabled = enabled; }

    public Long getUserId() { return userId; }
    public void setUserId(Long userId) { this.userId = userId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
}
