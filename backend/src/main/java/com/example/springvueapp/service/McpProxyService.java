package com.example.springvueapp.service;

import com.example.springvueapp.entity.McpServerInstanceEntity;
import com.example.springvueapp.mapper.McpInstanceMapper;
import com.example.springvueapp.mcp.client.McpClient;
import com.example.springvueapp.mcp.model.*;
import com.example.springvueapp.model.McpServerConfiguration;
import com.example.springvueapp.model.McpServerInstance;
import com.example.springvueapp.repository.McpServerInstanceRepository;
import com.example.springvueapp.sandbox.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 将 HTTP 请求代理到 MCP 服务器的服务
 */
@Service
public class McpProxyService {

    private static final Logger log = LoggerFactory.getLogger(McpProxyService.class);

    private final SandboxEnvironment sandboxEnvironment;
    private final McpServerInstanceRepository instanceRepository;
    private final McpInstanceMapper instanceMapper;
    private final Map<String, McpClient> activeClients = new ConcurrentHashMap<>();

    public McpProxyService(SandboxEnvironment sandboxEnvironment,
                          McpServerInstanceRepository instanceRepository,
                          McpInstanceMapper instanceMapper) {
        this.sandboxEnvironment = sandboxEnvironment;
        this.instanceRepository = instanceRepository;
        this.instanceMapper = instanceMapper;
    }

    /**
     * Start an MCP server instance
     */
    public Mono<McpServerInstance> startServer(McpServerConfiguration config, Long userId) {
        return createSandboxConfig(config)
                .flatMap(sandboxEnvironment::createSandbox)
                .flatMap(sandbox -> {
                    // Create instance record
                    McpServerInstance instance = McpServerInstance.builder()
                            .configurationId(config.getId())
                            .sandboxId(sandbox.getId())
                            .status(SandboxStatus.CREATED)
                            .sandboxType(sandboxEnvironment.getType())
                            .userId(userId)
                            .createdAt(LocalDateTime.now())
                            .build();

                    // 转换为Entity保存到数据库
                    McpServerInstanceEntity entity = instanceMapper.toEntity(instance);
                    return instanceRepository.save(entity)
                            .flatMap(savedEntity -> {
                                // Start the sandbox
                                return sandbox.start()
                                        .then(Mono.fromCallable(() -> {
                                            // Create MCP client
                                            McpClient client = new McpClient(sandbox);
                                            activeClients.put(sandbox.getId(), client);

                                            // Update instance status
                                            savedEntity.setStatus(SandboxStatus.RUNNING);
                                            savedEntity.setStartedAt(LocalDateTime.now());

                                            return savedEntity;
                                        }))
                                        .flatMap(instanceRepository::save)
                                        .map(instanceMapper::toDto);
                            });
                })
                .doOnSuccess(instance -> log.info("Started MCP server instance: {}", instance.getId()))
                .doOnError(error -> log.error("Failed to start MCP server", error));
    }

    /**
     * Stop an MCP server instance
     */
    public Mono<Void> stopServer(String sandboxId) {
        return instanceRepository.findBySandboxId(sandboxId)
                .flatMap(instance -> {
                    // Remove client
                    McpClient client = activeClients.remove(sandboxId);
                    if (client != null) {
                        client.close().subscribe();
                    }

                    // Stop sandbox
                    return sandboxEnvironment.getSandbox(sandboxId)
                            .flatMap(SandboxInstance::stop)
                            .then(sandboxEnvironment.destroySandbox(sandboxId))
                            .then(Mono.fromCallable(() -> {
                                instance.setStatus(SandboxStatus.STOPPED);
                                instance.setStoppedAt(LocalDateTime.now());
                                return instance;
                            }))
                            .flatMap(instanceRepository::save)
                            .then();
                })
                .doOnSuccess(v -> log.info("Stopped MCP server instance: {}", sandboxId))
                .doOnError(error -> log.error("Failed to stop MCP server: {}", sandboxId, error));
    }

    /**
     * Get available tools from an MCP server
     */
    public Mono<List<McpTool>> getTools(String sandboxId) {
        McpClient client = activeClients.get(sandboxId);
        if (client == null) {
            return Mono.error(new RuntimeException("MCP server not found or not running: " + sandboxId));
        }

        return client.listTools()
                .doOnSuccess(tools -> log.debug("Retrieved {} tools from server: {}", tools.size(), sandboxId));
    }

    /**
     * Call a tool on an MCP server
     */
    public Mono<McpToolCallResponse> callTool(String sandboxId, String toolName, Map<String, Object> arguments) {
        McpClient client = activeClients.get(sandboxId);
        if (client == null) {
            return Mono.error(new RuntimeException("MCP server not found or not running: " + sandboxId));
        }

        return client.callTool(toolName, arguments)
                .doOnSuccess(response -> log.debug("Called tool {} on server: {}", toolName, sandboxId))
                .doOnError(error -> log.error("Failed to call tool {} on server: {}", toolName, sandboxId, error));
    }

    /**
     * Get server information
     */
    public Mono<McpServerInfo> getServerInfo(String sandboxId) {
        McpClient client = activeClients.get(sandboxId);
        if (client == null) {
            return Mono.error(new RuntimeException("MCP server not found or not running: " + sandboxId));
        }

        return client.getServerInfo();
    }

    /**
     * Get all running instances for a user
     */
    public Flux<McpServerInstance> getRunningInstances(Long userId) {
        return instanceRepository.findByUserIdAndStatus(userId, SandboxStatus.RUNNING)
                .map(instanceMapper::toDto);
    }

    /**
     * Get instance status
     */
    public Mono<McpServerInstance> getInstanceStatus(String sandboxId) {
        return instanceRepository.findBySandboxId(sandboxId)
                .map(instanceMapper::toDto);
    }

    /**
     * Get resource usage for an instance
     */
    public Mono<SandboxResourceUsage> getResourceUsage(String sandboxId) {
        return sandboxEnvironment.getSandbox(sandboxId)
                .flatMap(SandboxInstance::getResourceUsage);
    }

    private Mono<SandboxConfig> createSandboxConfig(McpServerConfiguration config) {
        return Mono.fromCallable(() -> {
            SandboxConfig.Builder builder = SandboxConfig.builder()
                    .id(config.getName())
                    .name(config.getName())
                    .description(config.getDescription())
                    .dockerImage(config.getDockerImage())
                    .command(config.getCommand())
                    .workingDirectory(config.getWorkingDirectory())
                    .timeoutSeconds(config.getTimeoutSeconds())
                    .autoRestart(config.getAutoRestart());

            // 现在直接使用对象类型，不需要解析JSON
            if (config.getArguments() != null) {
                builder.arguments(config.getArguments());
            }

            if (config.getEnvironment() != null) {
                builder.environment(config.getEnvironment());
            }

            return builder.build();
        });
    }

    private List<String> parseArguments(String argumentsJson) {
        // Implement JSON parsing
        return null;
    }

    private Map<String, String> parseEnvironment(String environmentJson) {
        // Implement JSON parsing
        return null;
    }
}
